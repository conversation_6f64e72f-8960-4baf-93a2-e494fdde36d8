import React, { useEffect, memo, useCallback, useState, useRef } from "react";
import { Col, Row } from "antd";
import { Main, MainPage } from "./styled";
import ThongTinBenhNhan from "pages/thuNgan/quanLyTamUng/chiTietQuanLyTamUng/ThongTinBenhNhan";
import { useDispatch } from "react-redux";
import { useParams, useHistory, useLocation } from "react-router-dom";
import { CaretDownOutlined } from "@ant-design/icons";
import ModalChuyenKhoa from "pages/quanLyNoiTru/danhSachNguoiBenhNoiTru/ChiTietNguoiBenhNoiTru/Modal/ModalChuyenKhoa";
import ModalTamUng from "pages/thuNgan/quanLyTamUng/chiTietQuanLyTamUng/Modal/ModalTamUng";
import ModalTaoQrCode from "./Modal/ModalTaoQrCode";
import ModalTaoQrCodeLoi from "./Modal/ModalTaoQrCodeLoi";
import printProvider from "data-access/print-provider";
import ThongTinSoTien from "./ThongTinSoTien";
import ThuTamUng from "./ThuTamUng";
import HoanTamUng from "./HoanTamUng";
import DeNghiTamUng from "./DeNghiTamUng";
import HuyTamUng from "./HuyTamUng";
import DanhSachPhieuThu from "./DanhSachPhieuThu";
import PhongGiuongNoiTru from "./PhongGiuongNoiTru";
import { useTranslation } from "react-i18next";
import { checkRoleOr, checkRole } from "lib-utils/role-utils";
import {
  ROLES,
  HOTKEY,
  CACHE_KEY,
  THIET_LAP_CHUNG,
  TRANG_THAI_THANH_TOAN_QR,
  LOAI_PHUONG_THUC_TT,
  LOAI_MH_PHU,
  LOAI_QUAY,
  TRANG_THAI_PHIEU_THU_THANH_TOAN,
  TRANG_THAI_NB,
  DOI_TUONG_KCB,
  DOI_TUONG,
  VI_TRI_PHIEU_IN,
} from "constants/index";
import {
  Card,
  Tabs,
  Button,
  ChonQuay,
  ChonCaLamViec,
  TableWrapper,
  HeaderSearch as HeaderSearchTable,
} from "components";
import {
  useQueryString,
  useStore,
  useThietLap,
  useConfirm,
  useLoading,
  useGuid,
} from "hooks";
import HeaderSearch from "../HeaderSearch";
import { SVG } from "assets";
import { setQueryStringValue } from "hooks/useQueryString/queryString";
import {
  formatNumber,
  isArray,
  isNumber,
  openInNewTab,
  sleep,
} from "utils/index";
import { ISOFH_TOOL_HOST } from "client/request";
import { refConfirm } from "app";
import isofhToolProvider from "data-access/isofh-tool-provider";
import { toSafePromise } from "lib-utils";

const { useCaLamViec } = ChonCaLamViec;

const ChiTietQuanLyTamUng = (props) => {
  const { showConfirm } = useConfirm();
  const { t } = useTranslation();
  const history = useHistory();
  const { id } = useParams();
  const refChuyenKhoa = useRef(null);
  const refModalTamUng = useRef(null);
  const refModalTaoQrCode = useRef(null);
  const refModalTaoQrCodeLoi = useRef(null);
  const layerId = useGuid();
  const refClickBtnAdd = useRef();
  const refTimeout = useRef(null);
  const activeKey = useStore("deNghiTamUng.activeKey", "chiTietDeNghiTamUng");
  const thongTinBenhNhan = useStore("nbDotDieuTri.thongTinBenhNhan");
  const thongTinCoBan = useStore("nbDotDieuTri.thongTinCoBan");
  const [nhaTamUngId, setNhaTamUngId] = useQueryString("nhaTamUngId", null);
  const [tab] = useQueryString("tab", null);
  const [dataIN_PHIEU_KHI_DE_NGHI_TAM_UNG] = useThietLap(
    THIET_LAP_CHUNG.IN_PHIEU_KHI_DE_NGHI_TAM_UNG
  );
  const [dataKHONG_TAM_UNG_NB_DV_CHUA_THANH_TOAN, loadFinish] = useThietLap(
    THIET_LAP_CHUNG.KHONG_TAM_UNG_NB_DV_CHUA_THANH_TOAN
  );
  const [dataCANH_BAO_TAM_UNG_VOI_NB_NGOAI_TRU_DV] = useThietLap(
    THIET_LAP_CHUNG.CANH_BAO_TAM_UNG_VOI_NB_NGOAI_TRU_DV
  );
  const {
    lyDoTamUng: { getListAllLyDoTamUng },
    nbDotDieuTri: { getThongTinCoBan, getById, inTemNguoiBenh },
    maBenh: { getListAllMaBenh },
    phuongThucTT: { getListAllPhuongThucThanhToan },
    deNghiTamUng: {
      postDeNghiTamUng,
      onSearch: onSearchDeNghiTamUng,
      updateData: updateDataDeNghiTamUng,
    },
    thuTamUng: { postNbTamUng, inPhieuTamUng, onSearch: onSearchThuTamUng },
    phimTat: { onRegisterHotkey, onAddLayer, onRemoveLayer },
    quanLyTamUng: { onDelete },
    danhSachPhieuThu: { getDsPhieuThu },
    thuNgan: { taoQrThanhToan },
  } = useDispatch();
  const { state: locationState } = useLocation();
  const { showLoading, hideLoading } = useLoading();
  const [state, _setState] = useState({
    nhaTamUng: null,
  });
  const setState = (data = {}) => {
    _setState((preState) => {
      return { ...preState, ...data };
    });
  };
  const refCountSetCaLamViec = useRef(0);

  const [caLamViec, setCaLamViec] = useCaLamViec();

  useEffect(() => {
    if (caLamViec?.id && !refCountSetCaLamViec.current) {
      setCaLamViec(caLamViec);
      refCountSetCaLamViec.current++;
    }
  }, [caLamViec?.id]);
  // -------------------------------------------------------------------------
  const isNoiTru = [2, 3, 4, 6, 9].includes(thongTinBenhNhan?.doiTuongKcb);
  const listTabs = [
    ...(checkRoleOr([ROLES["THU_NGAN"].XEM_DS_DE_NGHI_TAM_UNG])
      ? [
          {
            name: t("thuNgan.quanLyTamUng.deNghiTamUng"),
            title: t("thuNgan.quanLyTamUng.danhSachDeNghiTamUng"),
            iconTab: <SVG.IcDeNghiTamTung />,
            component: <DeNghiTamUng />,
            routeTab: "chiTietDeNghiTamUng",
            showBox: true,
          },
        ]
      : []),
    ...(checkRoleOr([ROLES["THU_NGAN"].XEM_DS_THU_TAM_UNG])
      ? [
          {
            name: t("thuNgan.quanLyTamUng.thuTamUng"),
            title: t("thuNgan.quanLyTamUng.danhSachPhieuThuTamUng"),
            iconTab: <SVG.IcThuTamUng />,
            component: (
              <ThuTamUng
                nhaTamUngParent={state.nhaTamUng}
                nbDotDieuTriId={id}
              />
            ),
            routeTab: "chiTietThuTamUng",
            showBox: true,
          },
        ]
      : []),
    ...(checkRoleOr([ROLES["THU_NGAN"].XEM_DS_HOAN_TAM_UNG])
      ? [
          {
            name: t("thuNgan.quanLyTamUng.hoanTamUng"),
            title: t("thuNgan.quanLyTamUng.danhSachPhieuHoanTamUng"),
            iconTab: <SVG.IcHoanTamUng />,
            component: <HoanTamUng />,
            routeTab: "chiTietHoanTamUng",
            showBox: true,
          },
        ]
      : []),
    ...(checkRoleOr([ROLES["THU_NGAN"].XEM_DS_HUY_TAM_UNG])
      ? [
          {
            name: t("thuNgan.quanLyTamUng.huyTamUng"),
            title: t("thuNgan.quanLyTamUng.danhSachPhieuHuyTamUng"),
            iconTab: <SVG.IcHuyTamUng />,
            routeTab: "chiTietHuyTamUng",
            component: <HuyTamUng />,
            showBox: true,
          },
        ]
      : []),
    {
      name: t("thuNgan.phieuThuNgoaiTru"),
      title: t("thuNgan.quanLyTamUng.danhSachPhieuThuThanhToanNgoaiTru"),
      iconTab: <SVG.IcDsPhieuThu />,
      routeTab: "danhSachPhieuThu",
      component: <DanhSachPhieuThu nhaThuNganId={nhaTamUngId} />,
      showBox: false,
    },
    ...(isNoiTru && checkRole([ROLES["THU_NGAN"].HIEN_THI_PHONG_GIUONG_NOI_TRU])
      ? [
          {
            name: t("quanLyNoiTru.phongGiuong.title"),
            title: t("quanLyNoiTru.phongGiuong.title"),
            iconTab: <SVG.IcPhongGiuong />,
            routeTab: "phongGiuong",
            component: <PhongGiuongNoiTru />,
            showBox: false,
          },
        ]
      : []),
  ];
  // ------------------------------------------------------
  useEffect(() => {
    getListAllMaBenh({ page: "", size: "", active: true });
    getListAllLyDoTamUng({ page: "", size: "", active: true });
    getListAllPhuongThucThanhToan({ page: "", size: "", active: true });
    return () => {
      clearFunc(true);
    };
  }, []);
  useEffect(() => {
    if (state.nhaTamUng) {
      setNhaTamUngId(state.nhaTamUng?.toaNhaId);
    }
  }, [state.nhaTamUng]);

  useEffect(() => {
    if (id) {
      getThongTinCoBan(id);
      getById(id);
    }
  }, [id]);

  useEffect(() => {
    if (locationState?.thuTamUng) {
      updateDataDeNghiTamUng({ activeKey: "chiTietThuTamUng" });
    }
  }, [locationState]);

  useEffect(() => {
    if (tab) {
      updateDataDeNghiTamUng({ activeKey: tab });
      setQueryStringValue("tab", tab);
    }
  }, [tab]);

  const contentCanhBao = ({ data, id }) => {
    const onClickThanhToan = (item) => (e) => {
      e.preventDefault();
      e.stopPropagation();
      refConfirm.current && refConfirm.current.hide();
      history.push(
        `/thu-ngan/chi-tiet-phieu-thu/${item.maHoSo}/${item.id}/${id}`
      );
    };

    return (
      <div
        style={{
          display: "flex",
          flexDirection: "column",
          padding: "0 16px",
        }}
      >
        <div
          className="content content-2"
          style={{ color: "#45828d" }}
          dangerouslySetInnerHTML={{
            __html: t(
              "thuNgan.canhBaoNguoiBenhCoPhieuThuDvChuaThanhToanKhiVaoManTamUngContent"
            ),
          }}
        />
        <TableWrapper
          columns={[
            {
              title: <HeaderSearchTable title={t("common.stt")} />,
              width: 50,
              dataIndex: "index",
              key: "index",
              align: "center",
              render: (_, __, index) => index + 1,
            },
            {
              title: <HeaderSearchTable title={t("common.maHoSo")} />,
              width: 100,
              dataIndex: "maHoSo",
              key: "maHoSo",
            },
            {
              title: <HeaderSearchTable title={t("common.tenNb")} />,
              width: 150,
              dataIndex: "tenNb",
              key: "tenNb",
            },
            {
              title: (
                <HeaderSearchTable title={t("thuNgan.soTienChuaThanhToan")} />
              ),
              width: 100,
              dataIndex: "tongTien",
              key: "tongTien",
              align: "right",
              render: (field) => (field ? formatNumber(field) : ""),
            },
            {
              title: <HeaderSearchTable title={t("common.thaoTac")} />,
              width: 100,
              dataIndex: "thaoTac",
              key: "thaoTac",
              ignore: true,
              fixed: "right",
              render: (_, item) => {
                return (
                  <Button type="success" onClick={onClickThanhToan(item)}>
                    {t("thuNgan.thanhToanPT")}
                  </Button>
                );
              },
            },
          ]}
          dataSource={data}
        />
      </div>
    );
  };

  useEffect(() => {
    if (
      loadFinish &&
      dataKHONG_TAM_UNG_NB_DV_CHUA_THANH_TOAN?.eval() &&
      id &&
      (activeKey
        ? ["chiTietThuTamUng", "chiTietDeNghiTamUng"].includes(activeKey)
        : true) &&
      thongTinCoBan?.trangThaiNb === TRANG_THAI_NB.CHO_LAP_BENH_AN
    ) {
      getDsPhieuThu({
        nbDotDieuTriId: id,
        page: 0,
        size: 20,
        dsLoaiPhieuThu: [0, 1, 2, 3, 6, 10, 15],
        sort: "thanhToan,asc",
      }).then((res) => {
        if (isArray(res, true)) {
          let dsPhieuThu = res.filter(
            (item) =>
              item.thanhToan ===
                TRANG_THAI_PHIEU_THU_THANH_TOAN.CHUA_THANH_TOAN &&
              [1, 5, 7, 8, 10].includes(item.doiTuongKcb) &&
              item.doiTuong === 1
          );
          if (isArray(dsPhieuThu, true)) {
            refConfirm.current &&
              refConfirm.current.show({
                title: t("common.thongBao"),
                isContentElement: true,
                content: contentCanhBao({ data: dsPhieuThu, id }),
                showBtnCancel: false,
                showBtnOk: false,
                typeModal: "warning",
                width: 768,
                maskClosable: false,
              });
          }
        }
      });
    }
  }, [
    id,
    loadFinish,
    dataKHONG_TAM_UNG_NB_DV_CHUA_THANH_TOAN,
    activeKey,
    thongTinCoBan,
  ]);

  const onChange = (e) => {
    updateDataDeNghiTamUng({ activeKey: e });
    setQueryStringValue("tab", e);
  };

  const clearFunc = (isLeave = false) => {
    if (isLeave) {
      isofhToolProvider.putDataCenter({ ma: "LOAI_MH_PHU", value: null });
    }
    isofhToolProvider.putDataCenter({ ma: "QR_VALUE", value: null });
    isofhToolProvider.putDataCenter({ ma: "TT_NB", value: {} });
    isofhToolProvider.putDataCenter({ ma: "TT_TAM_UNG", value: {} });
  };

  const showModaConfirmRemove = (idPhieuThu, isThuTamUng) => {
    showConfirm(
      {
        title: "",
        content: `${
          isThuTamUng
            ? t("thuNgan.quanLyTamUng.xacNhanXoaPhieuThu")
            : t("thuNgan.quanLyTamUng.xacNhanXoaDeNghi")
        }`,
        cancelText: t("common.huy"),
        okText: t("common.dongY"),
        typeModal: "warning",
        showBtnOk: true,
      },
      () => {
        showLoading();
        onDelete(idPhieuThu)
          .then(() => {
            if (isThuTamUng) {
              onSearchThuTamUng({
                dataSearch: { nbDotDieuTriId: id, dsTrangThai: [40, 50, 15] },
              });
            } else {
              onSearchDeNghiTamUng({
                dataSearch: { nbDotDieuTriId: id, trangThai: 10 },
              });
            }
          })
          .finally(() => {
            hideLoading();
          });
      },
      () => {}
    );
  };

  const onTaoQrThanhToan = (item, isThuTamUng, inTemNb) => {
    showLoading({ title: t("thuNgan.dangGenQrCode"), width: 300 });
    taoQrThanhToan({
      nbDotDieuTriId: id,
      loai: 10,
      tuBanGhiId: item.id,
    })
      .then(async (res) => {
        const { trangThai, qr, phanHoi } = res?.data || {};
        if (phanHoi && phanHoi.code !== "00") {
          refModalTaoQrCodeLoi.current &&
            refModalTaoQrCodeLoi.current.show(
              { message: phanHoi?.message, isThuTamUng },
              () => {
                onTaoQrThanhToan(item, isThuTamUng, inTemNb);
              },
              () => {
                showModaConfirmRemove(item.id, isThuTamUng);
              }
            );
        } else if (!isThuTamUng && isNumber(dataIN_PHIEU_KHI_DE_NGHI_TAM_UNG)) {
          switch (Number(dataIN_PHIEU_KHI_DE_NGHI_TAM_UNG)) {
            case 1:
              onInPhieuTamUng(item.id);
              break;
            case 2:
            case 3:
              if (
                [
                  TRANG_THAI_THANH_TOAN_QR.MOI,
                  TRANG_THAI_THANH_TOAN_QR.TAO_QR,
                  TRANG_THAI_THANH_TOAN_QR.THANH_TOAN,
                ].includes(trangThai)
              ) {
                // in phiếu tạm ứng vs case = 3
                if (Number(dataIN_PHIEU_KHI_DE_NGHI_TAM_UNG) === 3) {
                  await onInPhieuTamUng(item.id);
                  await sleep(1500);
                }

                //đẩy dữ liệu sang mh phụ
                isofhToolProvider.putDataCenter({
                  ma: "LOAI_MH_PHU",
                  value: LOAI_MH_PHU.TAM_UNG,
                });
                isofhToolProvider.putDataCenter({
                  ma: "QR_VALUE",
                  value: res?.data,
                });
                isofhToolProvider.putDataCenter({
                  ma: "TT_NB",
                  value: thongTinBenhNhan,
                });
                isofhToolProvider.putDataCenter({
                  ma: "TT_TAM_UNG",
                  value: item,
                });

                refModalTaoQrCode.current &&
                  refModalTaoQrCode.current.show({ qrData: qr }, () => {
                    clearFunc(false);
                    if (refTimeout.current) {
                      clearInterval(refTimeout.current);
                    }
                  });
              } else if (TRANG_THAI_THANH_TOAN_QR.TAO_QR_LOI === trangThai) {
                refModalTaoQrCodeLoi.current &&
                  refModalTaoQrCodeLoi.current.show(
                    { isThuTamUng },
                    () => {
                      onTaoQrThanhToan(item, isThuTamUng);
                    },
                    () => {
                      showModaConfirmRemove(item.id, isThuTamUng);
                    }
                  );
              }
              break;
            case 0:
            default:
              break;
          }
        } else if (isThuTamUng) {
          if (item.trangThai !== LOAI_PHUONG_THUC_TT.CHUA_TAM_UNG) {
            onInPhieuTamUng(item.id);
          } else {
            //đẩy dữ liệu sang mh phụ
            isofhToolProvider.putDataCenter({
              ma: "LOAI_MH_PHU",
              value: LOAI_MH_PHU.TAM_UNG,
            });
            isofhToolProvider.putDataCenter({
              ma: "QR_VALUE",
              value: res?.data,
            });
            isofhToolProvider.putDataCenter({
              ma: "TT_NB",
              value: thongTinBenhNhan,
            });
            isofhToolProvider.putDataCenter({ ma: "TT_TAM_UNG", value: item });

            if (inTemNb) inTemNguoiBenh(id);
            refModalTaoQrCode.current &&
              refModalTaoQrCode.current.show({ qrData: qr }, () => {
                clearFunc(false);
                if (refTimeout.current) {
                  clearInterval(refTimeout.current);
                }
              });
          }
        } else {
          onInPhieuTamUng(item.id);
        }
      })
      .catch((err) => {
        refModalTaoQrCodeLoi.current &&
          refModalTaoQrCodeLoi.current.show(
            { message: err?.message, isThuTamUng },
            () => {
              onTaoQrThanhToan(item, isThuTamUng);
            },
            () => {
              showModaConfirmRemove(item.id, isThuTamUng);
            }
          );
      })
      .finally(() => {
        if (isThuTamUng) {
          onSearchThuTamUng({
            dataSearch: { nbDotDieuTriId: id, dsTrangThai: [40, 50, 15] },
          });
        } else {
          onSearchDeNghiTamUng({
            dataSearch: { nbDotDieuTriId: id, trangThai: 10 },
          });
        }
        hideLoading();
      });
  };

  const handleThemMoiDeNghiTamUng = () => {
    refModalTamUng.current &&
      refModalTamUng.current.show(
        {
          tab: "deNghiTamUng",
          action: postDeNghiTamUng,
          toaNhaId: state.nhaTamUng?.toaNhaId,
        },
        (idPhieuThu, isQrCode, trangThai, s) => {
          if (isQrCode) {
            setTimeout(() => {
              onTaoQrThanhToan(s?.data);
            }, 301);
          } else if (idPhieuThu) {
            onInPhieuTamUng(idPhieuThu);
          }
          onSearchDeNghiTamUng({
            dataSearch: { nbDotDieuTriId: id, trangThai: 10 },
          });
        }
      );
  };

  const onPrintAllPhieu = async ({
    idPhieuThu,
    inTemNb = false,
    delay = false,
  }) => {
    try {
      if (delay) {
        await sleep(400);
      }
      showLoading();
      if (idPhieuThu) {
        await toSafePromise(onInPhieuTamUng(idPhieuThu));
      }
      if (inTemNb) {
        await sleep(1000);
        await inTemNguoiBenh(id);
      }
    } catch (error) {
      console.log("error", error);
    } finally {
      hideLoading();
    }
  };

  const handleThemMoiThuTamUng = () => {
    const onShowModalTamUng = () => {
      refModalTamUng.current &&
        refModalTamUng.current.show(
          {
            tab: "thuTamUng",
            action: postNbTamUng,
            toaNhaId: state.nhaTamUng?.toaNhaId,
          },
          (idPhieuThu, isQrCode, trangThai, s, inTemNb) => {
            switch (s.code) {
              case 1028:
                refConfirm.current &&
                  refConfirm.current.show(
                    {
                      title: t("common.canhBao"),
                      content: t(
                        "thuNgan.quanLyTamUng.nguoiBenhCanSinhPhieuChi"
                      ),
                      cancelText: t("common.quayLai"),
                      okText: t("common.dongY"),
                      classNameOkText: "button-error",
                      showBtnOk: true,
                      typeModal: "error",
                    },
                    () => {
                      getDsPhieuThu({
                        nbDotDieuTriId: id,
                        nhoHonMucCungChiTra: true,
                        thanhToan: 50,
                      }).then((s) => {
                        openInNewTab(
                          `/thu-ngan/chi-tiet-phieu-thu/${s[0].maHoSo}/${s[0].id}/${id}`
                        );
                      });
                    }
                  );
                break;
              case 1036:
                let msg = s?.message || "";
                msg = msg.replace(
                  /\((.*?)\)/,
                  "<b style='color: red; font-size: 16px;text-transform: uppercase;'>($1)</b>"
                );
                showConfirm({
                  title: t("common.thongBao"),
                  content: msg,
                  cancelText: t("common.huy"),
                  classNameOkText: "button-warning",
                  showBtnOk: false,
                  typeModal: "warning",
                });
                break;

              default:
                if (isQrCode) {
                  setTimeout(() => {
                    onTaoQrThanhToan(s?.data, true, inTemNb);
                  }, 301);
                } else if (inTemNb || idPhieuThu) {
                  onPrintAllPhieu({ idPhieuThu, inTemNb, sleep: true });
                }
                onSearchThuTamUng({
                  dataSearch: {
                    nbDotDieuTriId: id,
                    dsTrangThai: [40, 50, 15],
                  },
                });
                break;
            }
          }
        );
    };

    const isCanhBaoTamUngVoiNBNgoaiTruDV =
      dataCANH_BAO_TAM_UNG_VOI_NB_NGOAI_TRU_DV?.eval() &&
      [
        DOI_TUONG_KCB.NGOAI_TRU,
        DOI_TUONG_KCB.DIEU_TRI_DAI_HAN_CO_KHAM_LINH_THUOC,
        DOI_TUONG_KCB.DIEU_TRI_DAI_HAN_CO_KHAM_SU_DUNG_THUOC,
      ].includes(thongTinCoBan.doiTuongKcb) &&
      thongTinCoBan.doiTuong === DOI_TUONG.KHONG_BAO_HIEM &&
      !thongTinCoBan.capCuu;

    if (isCanhBaoTamUngVoiNBNgoaiTruDV) {
      showConfirm(
        {
          title: t("common.canhBao"),
          typeModal: "warning",
          content: t(
            "thuNgan.nguoiBenhNgoaiTruKhongCoTheBhytBanCoChacChanMuonTiepTucTamUng"
          ),
          cancelText: t("common.huy"),
          okText: t("common.dongY"),
          showBtnOk: true,
        },
        onShowModalTamUng,
        () => {}
      );
      return;
    }
    onShowModalTamUng();
  };

  const isDoiTuongKcbNoiTru = [2, 3, 4, 6, 9].includes(
    thongTinBenhNhan?.doiTuongKcb
  );

  const paramsLienin = {
    maViTri: VI_TRI_PHIEU_IN.THU_NGAN.IN_PHIEU_THU_TAM_UNG,
    maPhieuIn: isDoiTuongKcbNoiTru ? "P1100" : "P1099",
  };

  const onInPhieuTamUng = async (idPhieuThu) => {
    try {
      //xử lý case nếu idPhieuThu là dạng mảng
      if (isArray(idPhieuThu, true)) {
        const resArr = await Promise.all(
          idPhieuThu.map(async (item) => {
            return await inPhieuTamUng(item, paramsLienin);
          })
        );

        await printProvider.printPdf((resArr || []).map((item) => item?.data));
      } else {
        const s = await inPhieuTamUng(idPhieuThu, paramsLienin);
        await printProvider.printPdf(s);
      }
    } catch (error) {
      console.error(error);
    }
  };

  refClickBtnAdd.current =
    activeKey === "chiTietDeNghiTamUng"
      ? handleThemMoiDeNghiTamUng
      : activeKey === "chiTietThuTamUng"
      ? handleThemMoiThuTamUng
      : null;

  useEffect(() => {
    onAddLayer({ layerId: layerId });
    onRegisterHotkey({
      layerId: layerId,
      hotKeys: [
        {
          keyCode: HOTKEY.F1, //F1
          onEvent: (e) => {
            refClickBtnAdd.current && refClickBtnAdd.current();
          },
        },
      ],
    });
    return () => {
      onRemoveLayer({ layerId: layerId });
    };
  }, []);

  const renderRightAction = (tab) => {
    switch (tab) {
      case 0:
        return (
          <>
            {checkRole([ROLES["THU_NGAN"].THEM_DE_NGHI_TAM_UNG]) && (
              <Button
                type="success"
                minWidth={100}
                onClick={handleThemMoiDeNghiTamUng}
              >
                {`${t("common.themMoi")} [F1]`}
              </Button>
            )}
          </>
        );
      case 1:
        return (
          <>
            <Button
              type="success"
              minWidth={100}
              onClick={handleThemMoiThuTamUng}
            >
              {`${t("common.themMoi")} [F1]`}
            </Button>
          </>
        );
    }
    return null;
  };

  return (
    <MainPage
      breadcrumb={[
        { link: "/thu-ngan", title: t("thuNgan.thuNgan") },
        {
          to: {
            pathname: `/thu-ngan/quan-ly-tam-ung`,
            search: locationState?.backUri || "",
          },
          title: t("thuNgan.quanLyTamUng.quanLyTamUng"),
        },
        {
          link: `/thu-ngan/quan-ly-tam-ung/${id}`,
          title: t("thuNgan.quanLyTamUng.chiTietDeNghiTamUng"),
        },
      ]}
      activeKey={activeKey}
    >
      <Main>
        <HeaderSearch layerId={layerId} />
        <Row>
          <div className="header-title">
            <h1>
              {activeKey === "phongGiuong"
                ? t("quanLyNoiTru.phongGiuong.title")
                : t(`thuNgan.quanLyTamUng.${activeKey}`)}
            </h1>
          </div>
          <div className="header-button">
            <div>
              <SVG.IcLichSu className="icon" />
              <b style={{ marginLeft: "5px" }}>{caLamViec?.ten}</b>
            </div>
            <ChonQuay
              dsLoaiQuay={LOAI_QUAY.THU_NGAN}
              cacheKey={CACHE_KEY.DATA_NHA_TAM_UNG}
              onChange={(value) => {
                setState({ nhaTamUng: value });
              }}
            />
          </div>
        </Row>
        <Row>
          <Col className="header-left" lg={{ span: 16 }} xl={{ span: 19 }}>
            <ThongTinBenhNhan />
          </Col>
          <Col
            className="header-right"
            lg={{ span: 8 }}
            xl={{ span: 5 }}
            style={{ paddingLeft: "20px" }}
          >
            <ThongTinSoTien />
          </Col>
        </Row>
        <Card className="content">
          <Tabs.Left
            activeKey={activeKey || "chiTietDeNghiTamUng"}
            tabPosition={"left"}
            type="card"
            moreIcon={<CaretDownOutlined />}
            onChange={onChange}
            className="tab-main"
            destroyInactiveTabPane
          >
            {[...listTabs].map((obj, i) => {
              return (
                <Tabs.TabPane
                  key={obj?.routeTab}
                  tab={
                    <div>
                      {obj?.iconTab}
                      {obj?.name}
                    </div>
                  }
                >
                  {obj?.showBox ? (
                    <Tabs.TabBox
                      title={obj?.title}
                      fixHeight={obj?.fixHeight}
                      rightAction={renderRightAction(i)}
                    >
                      {obj?.component}
                    </Tabs.TabBox>
                  ) : (
                    obj?.component
                  )}
                </Tabs.TabPane>
              );
            })}
          </Tabs.Left>
        </Card>
        <ModalChuyenKhoa refChuyenKhoa={refChuyenKhoa} />
        <ModalTamUng ref={refModalTamUng} />
        <ModalTaoQrCode nbDotDieuTriId={id} ref={refModalTaoQrCode} />
        <ModalTaoQrCodeLoi ref={refModalTaoQrCodeLoi} />
      </Main>
    </MainPage>
  );
};

export default memo(ChiTietQuanLyTamUng);
