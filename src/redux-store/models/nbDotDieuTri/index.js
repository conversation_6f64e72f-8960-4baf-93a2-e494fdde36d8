import nbDotDieuTriProvider from "data-access/nb-dot-dieu-tri-provider";
import nbMaBenhAnProvider from "data-access/nb-ma-benh-an-provider";
import { message } from "antd";
import { t } from "i18next";
import { cloneDeep, isEqual } from "lodash";
import nbTuVongProvider from "data-access/nb-tu-vong-provider";
import nbChiSoSongProvider from "data-access/nb-chi-so-song-provider";
import nbThongTinProvider from "data-access/nb-thong-tin-provider";
import { isArray } from "utils/index";
import { showError } from "utils/message-utils";
import printProvider from "data-access/print-provider";

const initialState = {
  isLoadingThongTinCoBan: false,
  isLoadingChiSoSong: false,
  data: {},
  thongTinBenhNhan: {},
  thongTinCoBan: {},
  chiPhiKb_Cb: {},
  listAllNbDotDieuTri: [],
  nbThongTinTuVong: {},
  thongTinHoSo: {},
  dsNbGoiDv: [],
  thongTinChiSoSong: {},
  thongTinBenhNhanTongHop: {},
};

export default {
  state: cloneDeep(initialState),
  reducers: {
    updateData(state, payload = {}) {
      return { ...state, ...payload };
    },
    clearData(state, payload = {}) {
      return { ...cloneDeep(initialState), ...payload };
    },
  },
  effects: (dispatch) => ({
    resetData: () => {
      dispatch.nbDotDieuTri.updateData(cloneDeep(initialState));
    },

    getById: (id, state) => {
      return new Promise((resolve, reject) => {
        nbDotDieuTriProvider
          .getById(id)
          .then((s) => {
            dispatch.nbDotDieuTri.updateData({ thongTinBenhNhan: s?.data });
            resolve(s);
          })
          .catch((e) => {
            reject(e);
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
          });
      });
    },
    getThongTinNb: ({ nbThongTinId, ...param }) => {
      if (!nbThongTinId) {
        dispatch.nbDotDieuTri.updateData({
          thongTinBenhNhan: {},
        });
        return;
      }
      return new Promise((resolve, reject) => {
        nbDotDieuTriProvider
          .searchNBDotDieuTriTongHop({ nbThongTinId, ...param })
          .then((s) => {
            if (s.data?.length) {
              dispatch.hoSoBenhAn.updateData({
                nbDotDieuTriId: s?.data[0].id,
                selectedMaHs: s?.data[0].maHoSo,
                lichSuKham: s?.data,
                soDotDieuTri: s?.data?.length,
              });
              resolve(s?.data[0]);
            } else {
              reject(s);
            }
          })
          .catch((e) => {
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            dispatch.nbDotDieuTri.updateData({
              thongTinBenhNhan: [],
            });
            reject(e);
          });
      });
    },
    searchNBDotDieuTriTongHop: (payload, state) => {
      return new Promise((resolve, reject) => {
        nbDotDieuTriProvider
          .searchNBDotDieuTriTongHop(payload)
          .then((s) => {
            resolve(s);
            dispatch.nbDotDieuTri.updateData({
              listAllNbDotDieuTri: s.data,
            });
          })
          .catch((e) => {
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            reject(e);
          });
      });
    },
    onUpdate: (payload, state) => {
      return new Promise((resolve, reject) => {
        nbDotDieuTriProvider
          .capNhatDotDieuTri(payload)
          .then((s) => {
            if (s?.code === 0) {
              dispatch.nbDotDieuTri.updateData({
                thongTinBenhNhan: s.data,
              });
              resolve(s);
            } else {
              reject(s);
              if (s?.code !== 7950 && s?.code !== 7920)
                message.error(s?.message);
            }
          })
          .catch((e) => {
            reject(e);
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
          });
      });
    },
    onUpdateSoNgayDieuTri: (payload, state) => {
      return new Promise((resolve, reject) => {
        nbDotDieuTriProvider
          .capNhatSoNgayDieuTri(payload)
          .then((s) => {
            if (s?.code === 0) {
              dispatch.nbDotDieuTri.updateData({
                thongTinBenhNhan: s.data,
              });
              resolve(s);
            } else {
              reject(s);
              if (s?.code !== 7950 && s?.code !== 7920)
                message.error(s?.message);
            }
          })
          .catch((e) => {
            reject(e);
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
          });
      });
    },
    getBangKeChiPhiKb_Cb: (payload, state) => {
      return new Promise((resolve, reject) => {
        nbDotDieuTriProvider
          .getBangKeChiPhiKb_Cb(payload)
          .then((s) => {
            if (s?.code === 0) {
              dispatch.nbDotDieuTri.updateData({
                chiPhiKb_Cb: s.data,
              });
              resolve(s);
            } else {
              resolve(s);
              if (s?.code !== 7950 && s?.code !== 7920)
                message.error(s?.message);
            }
          })
          .catch((e) => {
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
          });
      });
    },
    mienGiam: (payload, state) => {
      let { id } = payload;

      return new Promise((resolve, reject) => {
        nbDotDieuTriProvider
          .mienGiam(id, { ...payload, id: null })
          .then((s) => {
            if (s?.code === 0) {
              resolve(s);
              message.success(t("common.suaThongTinThanhCong"));
            } else {
              resolve(s);
              message.error(s?.message);
            }
          })
          .catch((e) => {
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
          });
      });
    },
    updateThongTinRaVien: (payload = {}, state) => {
      return new Promise((resolve, reject) => {
        nbDotDieuTriProvider
          .updateThongTinRaVien(payload)
          .then((s) => {
            message.success(t("common.capNhatThanhCong"));
            resolve(s?.data);
          })
          .catch((e) => {
            if (![8025, 8041].includes(e?.code)) {
              message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            }
            reject(e);
          });
      });
    },
    updateThongTinVaoVien: (payload = {}, state) => {
      return new Promise((resolve, reject) => {
        nbDotDieuTriProvider
          .updateThongTinVaoVien(payload)
          .then((s) => {
            message.success(t("common.capNhatThanhCong"));
            resolve(s?.data);
          })
          .catch((e) => {
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            reject(e);
          });
      });
    },
    putPhieuRaVien: (payload = {}, state) => {
      return new Promise((resolve, reject) => {
        nbDotDieuTriProvider
          .putPhieuRaVien(payload)
          .then((s) => {
            if (s?.code == 0) {
              message.success(t("common.capNhatThanhCong"));
              resolve(s?.data);
            } else {
              message.error(s?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
              reject(s);
            }
          })
          .catch((e) => {
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            reject(e);
          });
      });
    },
    getThongTinRaVien: (id) => {
      return new Promise((resolve, reject) => {
        nbDotDieuTriProvider
          .getThongTinRaVien(id)
          .then((s) => {
            dispatch.nbDotDieuTri.updateData({ nbThongTinRaVien: s?.data });
            resolve(s?.data);
          })
          .catch((e) => {
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            reject(e);
          });
      });
    },
    getThongTinVaoVien: (id) => {
      return new Promise((resolve, reject) => {
        nbDotDieuTriProvider
          .getThongTinVaoVien(id)
          .then((s) => {
            dispatch.nbDotDieuTri.updateData({ nbThongTinVaoVien: s?.data });
            resolve(s?.data);
          })
          .catch((e) => {
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            reject(e);
          });
      });
    },
    getThongTinTuVong: (id) => {
      return new Promise((resolve, reject) => {
        nbTuVongProvider
          .getById(id)
          .then((s) => {
            dispatch.nbDotDieuTri.updateData({ nbThongTinTuVong: s?.data });
            resolve(s?.data);
          })
          .catch((e) => {
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            reject(e);
          });
      });
    },

    updateThongTinTuVong: (payload = {}, state) => {
      return new Promise((resolve, reject) => {
        nbTuVongProvider
          .put(payload)
          .then((s) => {
            message.success(t("common.capNhatThanhCong"));
            resolve(s?.data);
          })
          .catch((e) => {
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            reject(e);
          });
      });
    },

    kiemTraRaVien: (payload = {}, state) => {
      return new Promise((resolve, reject) => {
        nbDotDieuTriProvider
          .kiemTraRaVien(payload)
          .then((s) => {
            resolve(s?.data);
          })
          .catch((e) => {
            showError(e?.message);
            reject(e);
          });
      });
    },

    getChiSoSong: ({ updateRedux = true, ...rest } = {}, state) => {
      return new Promise((resolve, reject) => {
        dispatch.nbDotDieuTri.updateData({
          thongTinChiSoSong: null,
          isLoadingChiSoSong: true,
        });
        nbChiSoSongProvider
          .getChiSoSongByNbDotDieuTriId(rest)
          .then((s) => {
            const dataChiSoSong = (data) => {
              let thongTinSinhHieu = null;
              data.sort(
                (a, b) =>
                  new Date(a?.thoiGianThucHien).getTime() -
                  new Date(b?.thoiGianThucHien).getTime()
              );
              if (isArray(data, true)) {
                thongTinSinhHieu = data[0];
              }
              return thongTinSinhHieu;
            };
            let _data = isArray(s?.data) ? dataChiSoSong(s?.data) : s?.data;
            if (updateRedux) {
              dispatch.nbDotDieuTri.updateData({
                thongTinChiSoSong: _data,
                isLoadingChiSoSong: false,
              });
            }
            resolve(_data);
          })
          .catch((err) => {
            reject(err);
            if (updateRedux) {
              dispatch.nbDotDieuTri.updateData({
                isLoadingChiSoSong: false,
              });
            }
          });
      });
    },
    clearThongTinNguoiBenh: () => {
      dispatch.toDieuTri.updateData({
        currentToDieuTri: null,
      });
      dispatch.danhSachNguoiBenhNoiTru.updateData({
        chiTietNguoiBenhNoiTru: null,
      });
      dispatch.nbDotDieuTri.updateData({
        thongTinBenhNhan: null,
        thongTinCoBan: null,
      });
    },
    getThongTinCoBan: (id, state, key = "thongTinCoBan") => {
      dispatch.nbDotDieuTri.updateData({ isLoadingThongTinCoBan: true });
      return new Promise((resolve, reject) => {
        nbDotDieuTriProvider
          .tongTienDieuTri(id)
          .then((s) => {
            if (!isEqual(state.nbDotDieuTri[key], s?.data)) {
              dispatch.nbDotDieuTri.updateData({
                [key]: s?.data,
                isLoadingThongTinCoBan: false,
              });
            } else {
              dispatch.nbDotDieuTri.updateData({
                isLoadingThongTinCoBan: false,
              });
            }
            resolve(s);
          })
          .catch((e) => {
            reject(e);
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            dispatch.nbDotDieuTri.updateData({
              isLoadingThongTinCoBan: false,
            });
          });
      });
    },
    getByTongHopId: (id, state) => {
      return new Promise((resolve, reject) => {
        nbDotDieuTriProvider
          .getByIdTongHop(id)
          .then((s) => {
            if (!isEqual(state.nbDotDieuTri.thongTinBenhNhanTongHop, s?.data)) {
              dispatch.nbDotDieuTri.updateData({
                thongTinBenhNhanTongHop: s?.data,
              });
            }
            resolve(s);
          })
          .catch((e) => {
            reject(e);
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
          });
      });
    },
    lienKetNb: (payload = {}, state) => {
      return new Promise((resolve, reject) => {
        nbDotDieuTriProvider
          .lienKetNb(payload)
          .then((s) => {
            message.success(t("common.capNhatThanhCong"));
            resolve(s?.data);
          })
          .catch((e) => {
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
          });
      });
    },
    getTtNbLienKet: (nbThongTinId, state) => {
      return new Promise((resolve, reject) => {
        nbThongTinProvider
          .getById(nbThongTinId)
          .then((s) => {
            resolve(s?.data);
          })
          .catch((e) => {
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
          });
      });
    },

    getDoiTuongKcb: (id, state) => {
      return new Promise((resolve, reject) => {
        nbDotDieuTriProvider
          .getDoiTuongKcb(id)
          .then((s) => {
            dispatch.nbDotDieuTri.updateData({ doiTuongKcb: s?.data });
            resolve(s);
          })
          .catch((e) => {
            reject(e);
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
          });
      });
    },
    luuLoiDan: ({ body, id }) => {
      return new Promise((resolve, reject) => {
        nbDotDieuTriProvider
          .loiDan(body, id)
          .then((s) => {
            dispatch.nbDotDieuTri.getThongTinRaVien(id);
            resolve(s.data);
            message.success(t("common.capNhatThanhCong"));
          })
          .catch((e) => {
            reject(e);
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
          });
      });
    },
    getPhieuBaoTu: (id) => {
      return new Promise((resolve, reject) => {
        nbDotDieuTriProvider
          .getPhieuBaoTu(id)
          .then((s) => {
            resolve(s?.data);
          })
          .catch((e) => {
            reject(e);
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
          });
      });
    },
    onDongBoNbSangGut: (payload) => {
      return new Promise((resolve, reject) => {
        nbDotDieuTriProvider
          .dongBoNguoiBenhGut(payload)
          .then((s) => {
            if (s?.code === 0) {
              message.success(
                t("tiepDon.dongBoNguoiBenhSangPhanMemGutThanhCong")
              );
              resolve(s?.data);
            } else {
              reject(s);
            }
          })
          .catch((e) => {
            reject(e);
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
          });
      });
    },
    onDongBoNguoiBenhHisCu: (payload) => {
      return new Promise((resolve, reject) => {
        nbDotDieuTriProvider
          .dongBoNguoiBenhHisCu(payload)
          .then((s) => {
            resolve(s);
          })
          .catch((e) => {
            reject(e);
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
          });
      });
    },
    onTaoMoiNbNgoaiVien: (payload) => {
      return new Promise((resolve, reject) => {
        nbDotDieuTriProvider
          .post(payload)
          .then((s) => {
            resolve(s?.data || {});
          })
          .catch((e) => {
            reject(e);
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
          });
      });
    },
    kiemTraSuaKhoaDieuTri: (payload) => {
      return new Promise((resolve, reject) => {
        nbDotDieuTriProvider
          .kiemTraSuaKhoaDieuTri(payload)
          .then((s) => {
            resolve(s?.data);
          })
          .catch((e) => {
            reject(e);
          });
      });
    },
    updateThongTinCapCuu: (payload = {}, state) => {
      return new Promise((resolve, reject) => {
        nbDotDieuTriProvider
          .updateThongTinCapCuu(payload)
          .then((s) => {
            message.success(t("common.capNhatThanhCong"));
            resolve(s?.data);
          })
          .catch((e) => {
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            reject(e);
          });
      });
    },
    traCuuLichSuKCB: (payload) => {
      return new Promise((resolve, reject) => {
        nbDotDieuTriProvider
          .traCuuLichSuKCB(payload)
          .then((s) => {
            if (s?.code == 0) {
              resolve(s?.data);
            } else {
              message.error(s?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
              reject(s);
            }
          })
          .catch((e) => {
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            reject(e);
          });
      });
    },
    traCuuBenhAnDienTu: nbDotDieuTriProvider.traCuuBenhAnDienTu,
    chuyenKhoaNgoaiTru: (payload = {}, state) => {
      return new Promise((resolve, reject) => {
        nbDotDieuTriProvider
          .chuyenKhoaNgoaiTru(payload)
          .then((s) => {
            if (s.code === 8073) {
              message.error(s?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            } else {
              message.success(s?.message || t("common.capNhatThanhCong"));
            }
            resolve(s?.data);
          })
          .catch((e) => {
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            reject(e);
          });
      });
    },
    inTemNguoiBenh: (nbDotDieuTriId) => {
      return new Promise((resolve, reject) => {
        nbDotDieuTriProvider
          .temNguoiBenh(nbDotDieuTriId)
          .then(async (s) => {
            await printProvider.printPdf(s);
            resolve(s);
          })
          .catch((e) => {
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            reject(e);
          });
      });
    },
  }),
};
